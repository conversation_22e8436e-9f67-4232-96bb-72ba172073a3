# Instalação das dependências necessárias
!pip install unidecode nltk scikit-learn transformers sentence-transformers torch

# Importações necessárias
import re
import unidecode
import nltk
import torch
import numpy as np
from nltk.tokenize import sent_tokenize, word_tokenize
from sklearn.feature_extraction.text import TfidfVectorizer
from transformers import AutoTokenizer, AutoModelForSequenceClassification
from sentence_transformers import SentenceTransformer, util
from sklearn.linear_model import LogisticRegression
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

# Download dos recursos do NLTK
nltk.download("punkt")
nltk.download("punkt_tab")

print("✅ Bibliotecas importadas e recursos baixados com sucesso!")

def preprocessar_geral(texto: str) -> dict:
    """
    Normaliza e limpa o texto para análise de PLN.
    Baseado nas especificações do projeto Carbon.
    """
    # 1. Normalização básica
    t = texto.lower().strip()
    t = unidecode.unidecode(t)   # remove acentos para padronização

    # 2. Remoção de caracteres especiais redundantes
    t = re.sub(r"[^a-z0-9áéíóúãõç\s\.\,\!\?]", " ", t)
    t = re.sub(r"\s+", " ", t).strip()

    # 3. Tokenização em sentenças e palavras
    sentencas = sent_tokenize(t, language="portuguese")
    tokens = [word_tokenize(s, language="portuguese") for s in sentencas]

    # 4. Saída organizada
    return {
        "texto_limpo": t,
        "sentencas": sentencas,
        "tokens_por_sentenca": tokens
    }

def preprocessar_conversa(conversa: list) -> list:
    """
    Processa uma conversa completa no formato:
    [00:00] PESSOA: texto da fala
    
    Retorna lista estruturada com timestamps e análises.
    """
    dados = []
    padrao = re.compile(r"\[(\d{2}:\d{2})\]\s*([A-ZÇÃÕÉÍÓÚ]+):\s*(.*)")

    for linha in conversa:
        m = padrao.match(linha)
        if not m:
            continue  # ignora linhas fora do formato esperado
        timestamp, speaker, fala = m.groups()
        dados.append({
            "timestamp": timestamp,
            "speaker": speaker.title(),  # normaliza para "Pessoa"
            "texto_original": fala,
            "preprocessado": preprocessar_geral(fala)
        })

    return dados

print("✅ Funções de pré-processamento definidas!")

# Inicialização dos modelos e vectorizers
print("🔄 Carregando modelos...")

# TF-IDF Vectorizer para análises baseadas em frequência
tfidf = TfidfVectorizer(ngram_range=(1,2))

# BERTimbau para análises contextuais
bert_tokenizer = AutoTokenizer.from_pretrained("neuralmind/bert-base-portuguese-cased")
# Nota: O modelo será carregado quando necessário para economizar memória

# Sentence Transformer para similaridade semântica
sbert = SentenceTransformer("paraphrase-multilingual-MiniLM-L12-v2")

# Classificadores que serão treinados
clf_hold = LogisticRegression()
clf_sondagem = LogisticRegression()

print("✅ Modelos carregados com sucesso!")

def to_seconds(ts_mmss: str) -> int:
    """
    Converte string 'MM:SS' para inteiro em segundos.
    Ex.: '01:23' -> 83
    """
    mm, ss = ts_mmss.split(":")
    return int(mm) * 60 + int(ss)

def preprocessar_tfidf(fala: dict):
    """Prepara texto para análise TF-IDF"""
    return tfidf.fit_transform([fala["preprocessado"]["texto_limpo"]])

def preprocessar_bert(fala: dict):
    """Prepara texto para análise com BERTimbau"""
    return bert_tokenizer(
        fala["preprocessado"]["texto_limpo"],
        return_tensors="pt",
        truncation=True,
        padding=True
    )

def preprocessar_embeddings(fala: dict):
    """Prepara texto para análise de embeddings"""
    return sbert.encode([fala["preprocessado"]["texto_limpo"]], normalize_embeddings=True)

def preprocessar_timestamps(fala: dict):
    """Extrai informações de timestamp"""
    return {
        "timestamp": fala.get("timestamp", None),
        "texto": fala["preprocessado"]["texto_limpo"]
    }

print("✅ Funções utilitárias definidas!")

# ========== FUNÇÕES DE APLICAÇÃO DOS ALGORITMOS ==========

def aplicar_regex(fala: dict, padrao: list) -> bool:
    """Aplica padrões regex no texto"""
    texto = fala["preprocessado"]["texto_limpo"]
    return any(re.search(p, texto) for p in padrao)

def aplicar_tfidf(fala: dict, clf, vectorizer) -> str:
    """Aplica classificação TF-IDF"""
    texto = fala["preprocessado"]["texto_limpo"]
    X = vectorizer.transform([texto])
    return clf.predict(X)[0]

def aplicar_bert(fala: dict, model, tokenizer, limiar=0.5) -> bool:
    """Aplica análise com BERTimbau"""
    texto = fala["preprocessado"]["texto_limpo"]
    inputs = tokenizer(texto, return_tensors="pt", truncation=True, padding=True)
    with torch.no_grad():
        probs = torch.softmax(model(**inputs).logits, dim=-1)[0]
    return probs[1].item() >= limiar

def aplicar_embeddings(fala: dict, templates, limiar=0.7) -> bool:
    """Aplica análise de similaridade com embeddings"""
    emb_fala = sbert.encode([fala["preprocessado"]["texto_limpo"]], normalize_embeddings=True)
    sims = util.cos_sim(emb_fala, templates).cpu().numpy()[0]
    return float(np.max(sims)) >= limiar

print("✅ Funções de aplicação de algoritmos definidas!")

# ========== PADRÕES E TEMPLATES ==========

# 1. Identificação da empresa (Comgás)
PADRAO_COMGAS = [r"\bcon?g[áa]s\b", r"\bsou\s+\w+\s+da\s+con?g[áa]s\b"]

# 2. Solicitação de dados pessoais
PADRAO_CPF = [r"\bcpf\b", r"\b\d{3}\.\d{3}\.\d{3}-\d{2}\b", r"\b\d{11}\b"]
PADRAO_CODIGO = [r"\bc[oó]d(igo)?\b", r"\bid\b"]

# 3. Confirmação de dados
PADRAO_EMAIL = [r"[a-z0-9._%+-]+@[a-z0-9.-]+\.[a-z]{2,}"]
PADRAO_TELEFONE = [r"\(?\d{2}\)?\s?9?\d{4}-?\d{4}"]
PADRAO_PROTOCOLO = [r"\b\d{6,}-\d{3}\b"]

# 4. Fraseologias de cordialidade
PADRAO_CORDIALIDADE = [r"\bpor favor\b", r"\bobrigad[oa]\b", r"\bcom licen[çc]a\b"]

# 5. Hold e retorno
PADRAO_HOLD = [r"\binstante\b", r"\baguarde\b", r"\bvou verificar\b"]
PADRAO_RETORNO = [r"\bobrigad[oa] por aguardar\b", r"\bretornando\b"]

# 6. Templates para embeddings
templates_queda = [
    "no caso de queda de ligação, posso retornar neste número?",
    "se a chamada cair, posso ligar de volta neste número?",
    "se cair a ligação, posso retornar para este telefone?"
]

templates_resolucao = [
    "o problema foi resolvido",
    "seu pedido está concluído", 
    "serviço restabelecido",
    "está tudo certo",
    "problema solucionado"
]

# Pré-computar embeddings dos templates
emb_queda = sbert.encode(templates_queda, normalize_embeddings=True)
emb_resolucao = sbert.encode(templates_resolucao, normalize_embeddings=True)

print("✅ Padrões e templates definidos!")

def analisar_abertura_encerramento(fala: dict) -> dict:
    """Análise de abertura/encerramento conforme projeto Carbon"""
    resultado = {}
    
    # Identificação da empresa
    resultado['citou_empresa'] = aplicar_regex(fala, PADRAO_COMGAS)
    
    # Cordialidade básica
    resultado['cordialidade'] = aplicar_regex(fala, PADRAO_CORDIALIDADE)
    
    # Pedido de contato em caso de queda (usando embeddings)
    resultado['perguntou_queda'] = aplicar_embeddings(fala, emb_queda, limiar=0.72)
    
    return resultado

def analisar_conducao_atendimento(fala: dict) -> dict:
    """Análise de condução do atendimento"""
    resultado = {}
    
    # Solicitação de dados
    resultado['solicitou_cpf'] = aplicar_regex(fala, PADRAO_CPF)
    resultado['solicitou_codigo'] = aplicar_regex(fala, PADRAO_CODIGO)
    
    # Hold e retorno
    resultado['indicou_hold'] = aplicar_regex(fala, PADRAO_HOLD)
    resultado['retornou_hold'] = aplicar_regex(fala, PADRAO_RETORNO)
    
    return resultado

def analisar_processos_procedimentos(fala: dict) -> dict:
    """Análise de processos e procedimentos"""
    resultado = {}
    
    # Confirmação de dados
    resultado['confirmou_email'] = aplicar_regex(fala, PADRAO_EMAIL)
    resultado['confirmou_telefone'] = aplicar_regex(fala, PADRAO_TELEFONE)
    resultado['forneceu_protocolo'] = aplicar_regex(fala, PADRAO_PROTOCOLO)
    
    return resultado

def analisar_resolucao(fala: dict) -> dict:
    """Análise de resolução do problema"""
    resultado = {}
    
    # Encerramento positivo (usando embeddings)
    resultado['encerramento_positivo'] = aplicar_embeddings(fala, emb_resolucao, limiar=0.70)
    
    return resultado

print("✅ Funções de análise específicas definidas!")

# Pesos para cada categoria (conforme documentação)
PESOS = {
    "abertura": 1.0,
    "conducao": 2.0,
    "processos": 1.5,
    "ncg": 3.0,  # Não Conformidades Graves
    "resolucao": 2.0
}

def calcular_score_categoria(analises: dict, categoria: str) -> float:
    """Calcula score para uma categoria específica"""
    if categoria == "abertura":
        # Score baseado em identificação da empresa e cordialidade
        pontos = 0
        if analises.get('citou_empresa', False): pontos += 0.6
        if analises.get('cordialidade', False): pontos += 0.4
        return min(pontos, 1.0)
    
    elif categoria == "conducao":
        # Score baseado em solicitação adequada de dados
        pontos = 0
        if analises.get('solicitou_cpf', False): pontos += 0.3
        if analises.get('solicitou_codigo', False): pontos += 0.3
        if analises.get('indicou_hold', False): pontos += 0.2
        if analises.get('retornou_hold', False): pontos += 0.2
        return min(pontos, 1.0)
    
    elif categoria == "processos":
        # Score baseado em confirmação de dados
        pontos = 0
        if analises.get('confirmou_email', False): pontos += 0.3
        if analises.get('confirmou_telefone', False): pontos += 0.3
        if analises.get('forneceu_protocolo', False): pontos += 0.4
        return min(pontos, 1.0)
    
    elif categoria == "ncg":
        # Score para Não Conformidades Graves (1.0 = sem problemas)
        return 1.0  # Placeholder - seria implementado com detecção de toxicidade
    
    elif categoria == "resolucao":
        # Score baseado em encerramento positivo
        return 1.0 if analises.get('encerramento_positivo', False) else 0.5
    
    return 0.0

def calcular_nota_final(scores_categorias: dict) -> float:
    """Calcula nota final ponderada (1-5)"""
    num = sum(scores_categorias[c] * PESOS[c] for c in scores_categorias)
    den = sum(PESOS.values())
    return round(1 + 4 * (num / den), 2)

def analisar_fala_completa(fala: dict) -> dict:
    """Análise completa de uma fala"""
    # Executar todas as análises
    analise_abertura = analisar_abertura_encerramento(fala)
    analise_conducao = analisar_conducao_atendimento(fala)
    analise_processos = analisar_processos_procedimentos(fala)
    analise_resolucao = analisar_resolucao(fala)
    
    # Combinar todas as análises
    todas_analises = {**analise_abertura, **analise_conducao, **analise_processos, **analise_resolucao}
    
    # Calcular scores por categoria
    scores = {
        "abertura": calcular_score_categoria(todas_analises, "abertura"),
        "conducao": calcular_score_categoria(todas_analises, "conducao"),
        "processos": calcular_score_categoria(todas_analises, "processos"),
        "ncg": calcular_score_categoria(todas_analises, "ncg"),
        "resolucao": calcular_score_categoria(todas_analises, "resolucao")
    }
    
    # Calcular nota final
    nota_final = calcular_nota_final(scores)
    
    return {
        "analises_detalhadas": todas_analises,
        "scores_por_categoria": scores,
        "nota_final": nota_final,
        "timestamp": fala.get("timestamp"),
        "speaker": fala.get("speaker"),
        "texto_original": fala.get("texto_original")
    }

print("✅ Sistema de scoring implementado!")

# Dados de exemplo baseados no seu código
conversa = [
    "[00:00] CLIENTE: Olá, tudo bem?",
    "[00:03] ATENDENTE: Boa tarde, sou a Júlia da Comgás, em que posso ajudar?",
    "[00:07] CLIENTE: Gostaria de verificar a minha conta de gás, acho que está com valor errado.",
    "[00:12] ATENDENTE: Certo, por favor me informe o seu CPF para localizar o cadastro.",
    "[00:15] CLIENTE: Claro, é 123.456.789-00.",
    "[00:20] ATENDENTE: Obrigada. Só um instante por favor, vou estar verificando as informações.",
    "[00:45] ATENDENTE: Obrigada por aguardar, encontrei aqui o cadastro.",
    "[00:48] ATENDENTE: Confere se o seu e-mail ainda é <EMAIL>?",
    "[00:52] CLIENTE: Sim, continua o mesmo.",
    "[00:55] ATENDENTE: Ok, já atualizei o sistema. Seu protocolo é 456789-123.",
    "[01:00] ATENDENTE: Verifiquei que o valor maior é porque houve um acúmulo de leitura.",
    "[01:05] CLIENTE: Entendi, então está tudo certo.",
    "[01:08] ATENDENTE: Isso mesmo, o problema está resolvido. Deseja aproveitar e ativar o débito automático?",
    "[01:12] CLIENTE: Não, obrigado.",
    "[01:15] ATENDENTE: Sem problemas. A Comgás agradece o seu contato, tenha uma boa tarde!",
    "[01:18] CLIENTE: Obrigado, boa tarde!"
]

# Processar a conversa
print("🔄 Processando conversa de exemplo...")
dados = preprocessar_conversa(conversa)

print(f"✅ Conversa processada: {len(dados)} falas identificadas")
print("\n📋 Primeiras 3 falas processadas:")
for i, fala in enumerate(dados[:3]):
    print(f"\n{i+1}. [{fala['timestamp']}] {fala['speaker']}: {fala['texto_original']}")
    print(f"   Texto limpo: {fala['preprocessado']['texto_limpo']}")

# Analisar apenas as falas do atendente
print("🔍 Analisando falas do atendente...")
print("=" * 60)

resultados_atendente = []
for i, fala in enumerate(dados):
    if fala['speaker'] == 'Atendente':
        resultado = analisar_fala_completa(fala)
        resultados_atendente.append(resultado)
        
        print(f"\n📞 Fala {i+1}: [{fala['timestamp']}]")
        print(f"💬 Texto: {fala['texto_original']}")
        print(f"📊 Nota: {resultado['nota_final']}/5.0")
        print(f"📈 Scores por categoria:")
        for cat, score in resultado['scores_por_categoria'].items():
            print(f"   • {cat.capitalize()}: {score:.2f}")
        
        print(f"🔍 Análises detectadas:")
        for analise, valor in resultado['analises_detalhadas'].items():
            if valor:
                print(f"   ✅ {analise}")

# Calcular estatísticas gerais
if resultados_atendente:
    notas = [r['nota_final'] for r in resultados_atendente]
    nota_media = sum(notas) / len(notas)
    
    print("\n" + "=" * 60)
    print("📊 RESUMO GERAL DA ANÁLISE")
    print("=" * 60)
    print(f"🎯 Nota média do atendimento: {nota_media:.2f}/5.0")
    print(f"📈 Melhor nota individual: {max(notas):.2f}/5.0")
    print(f"📉 Pior nota individual: {min(notas):.2f}/5.0")
    print(f"🗣️ Total de falas do atendente analisadas: {len(resultados_atendente)}")
    
    # Análise de conformidade
    conformidades = []
    for resultado in resultados_atendente:
        analises = resultado['analises_detalhadas']
        if analises.get('citou_empresa'): conformidades.append('Identificação da empresa')
        if analises.get('solicitou_cpf'): conformidades.append('Solicitação de CPF')
        if analises.get('forneceu_protocolo'): conformidades.append('Fornecimento de protocolo')
        if analises.get('encerramento_positivo'): conformidades.append('Encerramento positivo')
    
    print(f"\n✅ Conformidades identificadas:")
    for conf in set(conformidades):
        print(f"   • {conf}")
    
    # Classificação final
    if nota_media >= 4.0:
        classificacao = "EXCELENTE 🌟"
    elif nota_media >= 3.0:
        classificacao = "BOM ✅"
    elif nota_media >= 2.0:
        classificacao = "REGULAR ⚠️"
    else:
        classificacao = "PRECISA MELHORAR ❌"
    
    print(f"\n🏆 Classificação final: {classificacao}")
    
else:
    print("❌ Nenhuma fala do atendente foi encontrada para análise.")

print("\n✅ Análise completa finalizada!")