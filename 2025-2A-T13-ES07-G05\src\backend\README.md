# Backend

Abaixo está a estrutura de pastas e instruções de como executar a aplicação.

---

## 📂 Estrutura de Pastas

```
backend/
│── app/
│   │── main.py              # Ponto de entrada da aplicação
│   │── config.py            # Configurações gerais
│   │── dependencies.py      # Dependências reutilizáveis
│   │
│   ├── api/                 # Rotas organizadas por módulos
│   │   │── routers.py       # Inclui e organiza todos os endpoints
│   │      
│   │
│   ├── services/            # Regras de negócio
│   └── utils/               # Funções auxiliares
│
│── tests/                   # Testes unitários
│── README.md
```

---

## ▶️ Como Executar o Projeto

1. Puxe as dependências para a máquina:

```bash
npm i
```

2. Acesse a pasta raiz (`backend/`) e execute o servidor:

```bash
uvicorn app.main:app --reload
```

- `app.main:app` → significa **(pasta.arquivo:variável)**  
  - `app` = nome da pasta onde está o `main.py`  
  - `main` = nome do arquivo (`main.py`)  
  - `app` = variável FastAPI (`app = FastAPI()`)  

3. Acesse no navegador:
- [http://127.0.0.1:8000](http://127.0.0.1:8000) → rota raiz  
- [http://127.0.0.1:8000/docs](http://127.0.0.1:8000/docs) → documentação Swagger  

---

## ▶️ Executando Outros Arquivos

Se você quiser executar outro arquivo que também contenha um **FastAPI**, use o mesmo padrão:

```bash
uvicorn caminho_do_arquivo:nome_da_variavel

# Exemplo
uvicorn app.previsoes:app --reload # variável app do arquivo previsões, dentro da pasta app

```

Caso os comandos acima não funcionem, tente os comandos abaixo

```bash
python -m uvicorn caminho_do_arquivo:nome_da_variavel

python -m uvicorn app.previsoes:app --reload # variável app do arquivo previsões, dentro da pasta app

```
