import streamlit as st
import pandas as pd
import plotly.express as px

st.set_page_config(page_title="Dashboard", layout="wide")

# ===== Título =====
st.title("Dashboard")

# ===== Métricas (cards) =====
col1, col2, col3, col4, col5 = st.columns(5)

col1.metric("Zero Estrelas", "7,265", "+11.01%")
col2.metric("Uma Estrela", "3,671", "-0.03%")
col3.metric("Duas Estrelas", "7,265", "+11.01%")
col4.metric("Três Estrelas", "3,671", "-0.03%")
col5.metric("Quatro Estrelas", "7,265", "+11.01%")

# ===== Dados fictícios =====
meses = ["Jan", "Fev", "<PERSON>", "<PERSON>br", "<PERSON>", "<PERSON>"]
dados = pd.DataFrame({
    "Mês": meses,
    "Positivas": [10, 20, 30, 40, 35, 50],
    "Neutras": [15, 18, 20, 22, 21, 25],
    "Negativas": [5, 8, 12, 18, 25, 20]
})

# ===== Gráfico de linha =====
col_line, col_pie = st.columns([2, 1])

with col_line:
    st.subheader("Chamadas por mês")
    fig_line = px.line(
        dados,
        x="Mês",
        y=["Positivas", "Neutras", "Negativas"],
        markers=True,
        title="400.000 Chamadas analisadas"
    )
    st.plotly_chart(fig_line, use_container_width=True)

with col_pie:
    st.subheader("Avaliações por país")
    paises = pd.DataFrame({
        "País": ["United States", "Canada", "Mexico", "Other"],
        "Percentual": [52.1, 22.8, 13.9, 11.2]
    })
    fig_pie = px.pie(paises, names="País", values="Percentual", hole=0.4)
    st.plotly_chart(fig_pie, use_container_width=True)

# ===== Parte de baixo =====
col_bar, col_list = st.columns([2, 1])

# --- Gráfico de barras por semana ---
with col_bar:
    st.subheader("Chamadas por semana")
    semanas = ["q1", "q2", "q3", "q4"] * 3
    dados_semanais = pd.DataFrame({
        "Semana": semanas,
        "Positivas": [20, 25, 30, 28, 15, 18, 20, 22, 30, 35, 25, 27],
        "Neutras": [10, 15, 12, 14, 8, 10, 9, 11, 14, 12, 15, 10],
        "Negativas": [5, 8, 7, 6, 9, 10, 12, 11, 10, 9, 8, 7]
    })
    fig_bar = px.bar(
        dados_semanais,
        x="Semana",
        y=["Positivas", "Neutras", "Negativas"],
        barmode="stack",
        title="5.000 Chamadas analisadas"
    )
    st.plotly_chart(fig_bar, use_container_width=True)

# --- Avaliações como barras de progresso ---
with col_list:
    st.subheader("Avaliações")

    avaliacoes = {
        "Péssima": 0.10,
        "Ruim": 0.15,
        "Neutra": 0.25,
        "Boa": 0.30,
        "Ótima": 0.20
    }

    for label, value in avaliacoes.items():
        st.write(f"**{label}**: {int(value*100)}%")
        st.progress(value)
