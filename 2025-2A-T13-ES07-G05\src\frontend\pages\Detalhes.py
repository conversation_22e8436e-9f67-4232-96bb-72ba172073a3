import streamlit as st

st.set_page_config(page_title="Detalhes do Atendimento", layout="wide")

# ====== CSS global (dark mode forçado) ======
st.markdown("""
<style>
.stApp { background-color: #121212 !important; color: #ffffff !important; }
h1, h2, h3, h4, h5, h6, p, span, div, label { color: #ffffff !important; }
input[disabled], textarea[disabled] {
    background-color: #1e1e1e !important;
    color: #ffffff !important;
    border: 1px solid #333333 !important;
}
.box {
    border: 1px solid #333333;
    padding: 20px;
    border-radius: 10px;
    background: #1e1e1e;
    color: #ffffff;
}
.chip {
  display: inline-block;
  padding: 6px 14px;
  margin: 4px 8px 0 0;
  border-radius: 999px;
  color: #ffffff;
  font-size: 14px;
  font-weight: 600;
}
.chip.green  { background-color: #22c55e; }
.chip.purple { background-color: #7c3aed; }
.chip.brown  { background-color: #8d6e63; }
</style>
""", unsafe_allow_html=True)

# ====== Conteúdo ======
st.markdown("**Transcrição / Detalhes do Atendimento**")
st.title("Detalhes do Atendimento")

# ---- Campos de informações ----
c1, c2, c3 = st.columns(3)
with c1:
    st.text_input("ID", "IDdaChamada", disabled=True)
    st.text_input("Protocolo", "1234567890", disabled=True)
with c2:
    st.text_input("Avaliação", "4/5", disabled=True)
    st.text_input("Assunto", "Fatura", disabled=True)
with c3:
    st.text_input("Data", "29/08/2025", disabled=True)
    st.text_input("Atendente", "João Silva", disabled=True)

# ---- Chips ----
st.markdown("""
<div>
  <span class="chip green">Cálculo do consumo</span>
  <span class="chip purple">Vistoria agendada</span>
  <span class="chip brown">Parcelamento da fatura</span>
</div>
""", unsafe_allow_html=True)

# ---- Caixa de transcrição ----
st.markdown("""
<div class="box" style="margin-top:14px">
<b>Cliente:</b><br>
Alô, bom dia. Eu tô ligando porque recebi minha fatura da Congás e o valor veio muito acima do normal.
Geralmente pago uns 120 reais, mas dessa vez veio quase 400. Não faz sentido.<br><br>

<b>Atendente:</b><br>
Bom dia! Poxa, entendo sua preocupação, realmente esse aumento é bem fora do comum.
Vamos verificar juntos, tudo bem? Você pode me confirmar o número do seu contrato ou CPF, por favor?<br><br>

<b>Cliente:</b><br>
Claro, é o CPF 123.456.789-00.<br><br>

<b>Atendente:</b><br>
Perfeito, já localizei aqui. Olhando o histórico, percebo que houve um acúmulo de consumo não registrado no mês anterior —
às vezes o medidor não envia os dados na hora certa e o valor acaba sendo lançado na próxima fatura.<br><br>

<b>Cliente:</b><br>
Ah, mas aí fica pesado demais pra mim pagar tudo de uma vez, né? Não tem como resolver isso de outra forma?<br><br>

<b>Atendente:</b><br>
Sim, com certeza. O que podemos fazer é dividir esse valor acumulado em parcelas, pra não pesar tanto no seu bolso.
Além disso, já vou abrir uma solicitação de vistoria no medidor, pra garantir que não tenha nenhum problema técnico.<br><br>

<b>Cliente:</b><br>
Tá, gostei dessa opção. E como funciona essa vistoria?<br><br>

<b>Atendente:</b><br>
Um técnico da Congás vai até sua residência dentro de 5 dias úteis para verificar se o medidor está funcionando corretamente.
Você recebe a confirmação da data por SMS e e-mail.
</div>
""", unsafe_allow_html=True)
